<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('exam_results', function (Blueprint $table) {
            $table->integer('pilihan_ganda_score')->default(0)->after('score');
            $table->integer('uraian_singkat_score')->default(0)->after('pilihan_ganda_score');
            $table->integer('esai_score')->default(0)->after('uraian_singkat_score');
            $table->timestamp('completed_at')->nullable()->after('submitted_at');
            $table->boolean('auto_completed')->default(false)->after('completed_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('exam_results', function (Blueprint $table) {
            $table->dropColumn([
                'pilihan_ganda_score',
                'uraian_singkat_score',
                'esai_score',
                'completed_at',
                'auto_completed'
            ]);
        });
    }
};
