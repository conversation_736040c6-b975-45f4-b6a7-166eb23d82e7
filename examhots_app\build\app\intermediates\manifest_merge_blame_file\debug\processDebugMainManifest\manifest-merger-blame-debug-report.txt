1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.examhots_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:6:5-67
15-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:6:22-64
16    <!-- Permissions for camera and file access -->
17    <uses-permission android:name="android.permission.CAMERA" />
17-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:3:5-65
17-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:3:22-62
18    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
18-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:4:5-80
18-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:4:22-77
19    <uses-permission
19-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:5:5-81
20        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
20-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:5:22-78
21        android:maxSdkVersion="28" />
21-->[:camera_android_camerax] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-35
22    <!--
23 Required to query activities that can process text, see:
24         https://developer.android.com/training/package-visibility and
25         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
26
27         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
28    -->
29    <queries>
29-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:44:5-49:15
30        <intent>
30-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:45:9-48:18
31            <action android:name="android.intent.action.PROCESS_TEXT" />
31-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:46:13-73
31-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:46:21-70
32
33            <data android:mimeType="text/plain" />
33-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:47:13-51
33-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:47:19-48
34        </intent>
35        <intent>
35-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-12:18
36            <action android:name="android.intent.action.GET_CONTENT" />
36-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-72
36-->[:file_picker] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\file_picker\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:21-69
37
38            <data android:mimeType="*/*" />
38-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:47:13-51
38-->C:\xampp-8.2-new\htdocs\examhots\examhots_app\android\app\src\main\AndroidManifest.xml:47:19-48
39        </intent>
40    </queries>
41
42    <uses-feature android:name="android.hardware.camera.any" />
42-->[:camera_android_camerax] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
42-->[:camera_android_camerax] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
43
44    <uses-permission android:name="android.permission.RECORD_AUDIO" />
44-->[:camera_android_camerax] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
44-->[:camera_android_camerax] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-68
45
46    <permission
46-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
47        android:name="com.example.examhots_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
47-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
48        android:protectionLevel="signature" />
48-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
49
50    <uses-permission android:name="com.example.examhots_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
50-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
50-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
51
52    <application
53        android:name="android.app.Application"
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43a41165a90de7b0bfc02b6ed57eebd0\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
55        android:debuggable="true"
56        android:extractNativeLibs="true"
57        android:icon="@mipmap/launcher_icon"
58        android:label="Hots UM" >
59        <activity
60            android:name="com.example.examhots_app.MainActivity"
61            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
62            android:exported="true"
63            android:hardwareAccelerated="true"
64            android:launchMode="singleTop"
65            android:taskAffinity=""
66            android:theme="@style/LaunchTheme"
67            android:windowSoftInputMode="adjustResize" >
68
69            <!--
70                 Specifies an Android theme to apply to this Activity as soon as
71                 the Android process has started. This theme is visible to the user
72                 while the Flutter UI initializes. After that, this theme continues
73                 to determine the Window background behind the Flutter UI.
74            -->
75            <meta-data
76                android:name="io.flutter.embedding.android.NormalTheme"
77                android:resource="@style/NormalTheme" />
78
79            <intent-filter>
80                <action android:name="android.intent.action.MAIN" />
81
82                <category android:name="android.intent.category.LAUNCHER" />
83            </intent-filter>
84        </activity>
85        <!--
86             Don't delete the meta-data below.
87             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
88        -->
89        <meta-data
90            android:name="flutterEmbedding"
91            android:value="2" />
92
93        <service
93-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f2422feca0a64dbc3f726e900092f\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
94            android:name="androidx.camera.core.impl.MetadataHolderService"
94-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f2422feca0a64dbc3f726e900092f\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
95            android:enabled="false"
95-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f2422feca0a64dbc3f726e900092f\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
96            android:exported="false" >
96-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f2422feca0a64dbc3f726e900092f\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
97            <meta-data
97-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f2422feca0a64dbc3f726e900092f\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
98                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
98-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f2422feca0a64dbc3f726e900092f\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
99                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
99-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\0c3f2422feca0a64dbc3f726e900092f\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
100        </service>
101
102        <provider
102-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-17:20
103            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
103-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-82
104            android:authorities="com.example.examhots_app.flutter.image_provider"
104-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-74
105            android:exported="false"
105-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-37
106            android:grantUriPermissions="true" >
106-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-47
107            <meta-data
107-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-16:75
108                android:name="android.support.FILE_PROVIDER_PATHS"
108-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-67
109                android:resource="@xml/flutter_image_picker_file_paths" />
109-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:17-72
110        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
111        <service
111-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:9-31:19
112            android:name="com.google.android.gms.metadata.ModuleDependencies"
112-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-78
113            android:enabled="false"
113-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-36
114            android:exported="false" >
114-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-37
115            <intent-filter>
115-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-26:29
116                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
116-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:17-94
116-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:25-91
117            </intent-filter>
118
119            <meta-data
119-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-30:36
120                android:name="photopicker_activity:0:required"
120-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:17-63
121                android:value="" />
121-->[:image_picker_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\image_picker_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-33
122        </service>
123
124        <activity
124-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-11:74
125            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
125-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-74
126            android:exported="false"
126-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-37
127            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" />
127-->[:url_launcher_android] C:\xampp-8.2-new\htdocs\examhots\examhots_app\build\url_launcher_android\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-71
128
129        <provider
129-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
130            android:name="androidx.startup.InitializationProvider"
130-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:25:13-67
131            android:authorities="com.example.examhots_app.androidx-startup"
131-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:26:13-68
132            android:exported="false" >
132-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:27:13-37
133            <meta-data
133-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
134-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
135                android:value="androidx.startup" />
135-->[androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\43ebce9c1ba25a34b2589ddcb28a686a\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
136            <meta-data
136-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
137                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
137-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
138                android:value="androidx.startup" />
138-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
139        </provider>
140
141        <uses-library
141-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
142            android:name="androidx.window.extensions"
142-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
143            android:required="false" />
143-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
144        <uses-library
144-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
145            android:name="androidx.window.sidecar"
145-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
146            android:required="false" />
146-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\970764a81dd610c772147726baa7dca5\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
147
148        <receiver
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
149            android:name="androidx.profileinstaller.ProfileInstallReceiver"
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
150            android:directBootAware="false"
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
151            android:enabled="true"
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
152            android:exported="true"
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
153            android:permission="android.permission.DUMP" >
153-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
154            <intent-filter>
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
155                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
156            </intent-filter>
157            <intent-filter>
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
158                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
159            </intent-filter>
160            <intent-filter>
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
161                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
162            </intent-filter>
163            <intent-filter>
163-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
164                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.10.2\transforms\eabc1325411a824ae74b9b450245750e\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
165            </intent-filter>
166        </receiver>
167    </application>
168
169</manifest>
