import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'services/api_service.dart';
import 'models/question_material.dart';

class BuatSoalPilihanGandaPage extends StatefulWidget {
  final QuestionMaterial material;

  const BuatSoalPilihanGandaPage({super.key, required this.material});

  @override
  State<BuatSoalPilihanGandaPage> createState() =>
      _BuatSoalPilihanGandaPageState();
}

class _BuatSoalPilihanGandaPageState extends State<BuatSoalPilihanGandaPage> {
  final TextEditingController _questionController = TextEditingController();
  final List<TextEditingController> _answerControllers = [
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
    TextEditingController(),
  ];
  final ImagePicker _picker = ImagePicker();
  List<XFile> _selectedImages = [];
  int _correctAnswerIndex = 0; // Index of correct answer (default: A)
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    // No default text - will use placeholders instead
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF333333)),
          onPressed: () => Navigator.pop(context),
        ),
        title: const Text(
          'Buat Soal Baru',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF333333),
          ),
        ),
        centerTitle: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                image: const DecorationImage(
                  image: AssetImage('assets/image-bg.jpg'),
                  fit: BoxFit.cover,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFF28A745).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.radio_button_checked,
                      color: Color(0xFF28A745),
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Pilihan Ganda',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF333333),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          'Buat soal dan jawaban untuk melengkapi bank soal.',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Progress Bar
            Row(
              children: [
                Expanded(
                  child: Container(
                    height: 6,
                    decoration: BoxDecoration(
                      color: const Color(0xFF455A9D),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Container(
                    height: 6,
                    decoration: BoxDecoration(
                      color: const Color(0xFF455A9D),
                      borderRadius: BorderRadius.circular(3),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 32),

            // Gambar Section
            const Text(
              'Gambar',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF333333),
              ),
            ),
            const SizedBox(height: 12),

            Container(
              height: 120,
              decoration: BoxDecoration(
                border: Border.all(
                  color: const Color(0xFFE9ECEF),
                  style: BorderStyle.solid,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child:
                  _selectedImages.isEmpty
                      ? Center(
                        child: GestureDetector(
                          onTap: _pickImages,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add_photo_alternate_outlined,
                                size: 32,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 8),
                              Text(
                                'Pilih Gambar',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )
                      : ListView.builder(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.all(8),
                        itemCount:
                            _selectedImages.length + 1, // +1 for add button
                        itemBuilder: (context, index) {
                          if (index == _selectedImages.length) {
                            // Add more images button
                            return GestureDetector(
                              onTap: _pickImages,
                              child: Container(
                                width: 100,
                                margin: const EdgeInsets.only(right: 8),
                                decoration: BoxDecoration(
                                  color: const Color(0xFFF8F9FA),
                                  borderRadius: BorderRadius.circular(6),
                                  border: Border.all(
                                    color: const Color(0xFFE9ECEF),
                                    style: BorderStyle.solid,
                                  ),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.add,
                                      size: 24,
                                      color: Colors.grey[400],
                                    ),
                                    const SizedBox(height: 4),
                                    Text(
                                      'Tambah',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }

                          // Selected image
                          return Container(
                            width: 100,
                            margin: const EdgeInsets.only(right: 8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Stack(
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(6),
                                  child: Image.file(
                                    File(_selectedImages[index].path),
                                    fit: BoxFit.cover,
                                    width: double.infinity,
                                    height: double.infinity,
                                  ),
                                ),
                                Positioned(
                                  top: 4,
                                  right: 4,
                                  child: GestureDetector(
                                    onTap: () => _removeImage(index),
                                    child: Container(
                                      width: 20,
                                      height: 20,
                                      decoration: const BoxDecoration(
                                        color: Colors.black54,
                                        shape: BoxShape.circle,
                                      ),
                                      child: const Icon(
                                        Icons.close,
                                        color: Colors.white,
                                        size: 12,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
            ),
            const SizedBox(height: 8),
            const Text(
              'Supported formats : JPG/JPEG/PNG',
              style: TextStyle(fontSize: 12, color: Color(0xFF666666)),
            ),
            const SizedBox(height: 24),

            // Pertanyaan Section
            const Row(
              children: [
                Text(
                  'Pertanyaan',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
                Text(
                  '*',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            Container(
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFFE9ECEF)),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextField(
                controller: _questionController,
                maxLines: 4,
                decoration: const InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.all(16),
                  hintText:
                      'e.g. Organ pernapasan manusia yang berfungsi sebagai tempat pertukaran oksigen dan karbon dioksida adalah...',
                  hintStyle: TextStyle(color: Color(0xFF999999), fontSize: 14),
                ),
                style: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
              ),
            ),
            const SizedBox(height: 24),

            // Jawaban Section
            Row(
              children: [
                const Text(
                  'Jawaban',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF333333),
                  ),
                ),
                const Text(
                  '*',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // Instruction for correct answer
            const Text(
              'Pilih jawaban yang benar dengan mencentang radio button',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFF666666),
                fontStyle: FontStyle.italic,
              ),
            ),
            const SizedBox(height: 12),

            // Answer Options
            ..._buildAnswerOptions(),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    side: const BorderSide(color: Color(0xFF455A9D)),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: const Text(
                    'Kembali',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF455A9D),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                flex: 2,
                child: ElevatedButton(
                  onPressed: _isLoading ? null : _simpanSoal,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF455A9D),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    'Tambah Soal',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<Widget> _buildAnswerOptions() {
    return _answerControllers.asMap().entries.map((entry) {
      int index = entry.key;
      TextEditingController controller = entry.value;
      bool isCorrect = _correctAnswerIndex == index;

      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  isCorrect ? const Color(0xFF455A9D) : const Color(0xFFE9ECEF),
              width: isCorrect ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isCorrect ? const Color(0xFFF8F9FA) : Colors.white,
          ),
          child: Row(
            children: [
              // Radio button for correct answer
              Container(
                width: 40,
                height: 48,
                decoration: BoxDecoration(
                  color:
                      isCorrect
                          ? const Color(0xFF455A9D)
                          : const Color(0xFFF8F9FA),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                ),
                child: Center(
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _correctAnswerIndex = index;
                      });
                    },
                    child: Container(
                      width: 20,
                      height: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color:
                              isCorrect
                                  ? Colors.white
                                  : const Color(0xFFCED4DA),
                          width: 2,
                        ),
                        color: isCorrect ? Colors.white : Colors.transparent,
                      ),
                      child:
                          isCorrect
                              ? const Icon(
                                Icons.circle,
                                size: 8,
                                color: Color(0xFF455A9D),
                              )
                              : null,
                    ),
                  ),
                ),
              ),

              // Answer letter
              Container(
                width: 40,
                height: 48,
                decoration: BoxDecoration(
                  color:
                      isCorrect
                          ? const Color(0xFF455A9D).withValues(alpha: 0.1)
                          : const Color(0xFFF8F9FA),
                ),
                child: Center(
                  child: Text(
                    String.fromCharCode(65 + index), // A, B, C, D...
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color:
                          isCorrect
                              ? const Color(0xFF455A9D)
                              : const Color(0xFF333333),
                    ),
                  ),
                ),
              ),

              Expanded(
                child: TextField(
                  controller: controller,
                  decoration: InputDecoration(
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 14,
                    ),
                    hintText:
                        'Masukkan jawaban ${String.fromCharCode(65 + index)}',
                    hintStyle: const TextStyle(
                      color: Color(0xFF999999),
                      fontSize: 14,
                    ),
                  ),
                  style: TextStyle(
                    fontSize: 14,
                    color:
                        isCorrect
                            ? const Color(0xFF455A9D)
                            : const Color(0xFF333333),
                    fontWeight: isCorrect ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }).toList();
  }

  Future<void> _pickImages() async {
    try {
      final List<XFile> images = await _picker.pickMultiImage(imageQuality: 80);
      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error memilih gambar: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  @override
  void dispose() {
    _questionController.dispose();
    for (var controller in _answerControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _simpanSoal() async {
    // Validate form
    if (!_validateForm()) {
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Prepare answers data
      List<Map<String, String>> answers = [];
      for (int i = 0; i < _answerControllers.length; i++) {
        if (_answerControllers[i].text.trim().isNotEmpty) {
          answers.add({'text': _answerControllers[i].text.trim()});
        }
      }

      // Create question via API (web-compatible)
      final response = await ApiService.createQuestionWithFiles(
        materialId: widget.material.id,
        question: _questionController.text.trim(),
        type: 'pilihan_ganda',
        imageFiles: _selectedImages.isNotEmpty ? _selectedImages : null,
        answers: answers,
        correctAnswerIndex: _correctAnswerIndex,
      );

      if (response['success'] == true) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Soal berhasil disimpan!'),
              backgroundColor: Color(0xFF455A9D),
            ),
          );
          Navigator.pop(context, true); // Return true to indicate success
        }
      } else {
        throw Exception(response['message'] ?? 'Failed to save question');
      }
    } catch (e) {
      setState(() {
        _errorMessage = e.toString().replaceAll('Exception: ', '');
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $_errorMessage'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  bool _validateForm() {
    // Validate question
    if (_questionController.text.trim().isEmpty) {
      _showErrorDialog('Pertanyaan tidak boleh kosong');
      return false;
    }

    // Validate answers
    int filledAnswers = 0;
    for (TextEditingController controller in _answerControllers) {
      if (controller.text.trim().isNotEmpty) {
        filledAnswers++;
      }
    }

    if (filledAnswers < 2) {
      _showErrorDialog('Minimal harus ada 2 pilihan jawaban');
      return false;
    }

    // Validate correct answer
    if (_correctAnswerIndex >= _answerControllers.length ||
        _answerControllers[_correctAnswerIndex].text.trim().isEmpty) {
      _showErrorDialog('Pilih jawaban yang benar');
      return false;
    }

    return true;
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Validasi Error'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }
}
