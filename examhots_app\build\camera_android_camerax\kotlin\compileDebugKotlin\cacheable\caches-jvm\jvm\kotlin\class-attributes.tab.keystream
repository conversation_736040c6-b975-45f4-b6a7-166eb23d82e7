4io.flutter.plugins.camerax.CameraXLibraryPigeonUtils'io.flutter.plugins.camerax.CameraXError><EMAIL>@io.flutter.plugins.camerax.CameraXLibraryPigeonProxyApiBaseCodec5io.flutter.plugins.camerax.InfoSupportedHardwareLevel?io.flutter.plugins.camerax.InfoSupportedHardwareLevel.Companion&io.flutter.plugins.camerax.AspectRatio0io.flutter.plugins.camerax.AspectRatio.Companion*io.flutter.plugins.camerax.CameraStateType4io.flutter.plugins.camerax.CameraStateType.Companion0io.flutter.plugins.camerax.LiveDataSupportedType:io.flutter.plugins.camerax.LiveDataSupportedType.Companion'io.flutter.plugins.camerax.VideoQuality1io.flutter.plugins.camerax.VideoQuality.Companion'io.flutter.plugins.camerax.MeteringMode1io.flutter.plugins.camerax.MeteringMode.Companion%io.flutter.plugins.camerax.LensFacing/io.flutter.plugins.camerax.LensFacing.Companion+io.flutter.plugins.camerax.CameraXFlashMode5io.flutter.plugins.camerax.CameraXFlashMode.Companion9io.flutter.plugins.camerax.ResolutionStrategyFallbackRuleCio.flutter.plugins.camerax.ResolutionStrategyFallbackRule.Companion:io.flutter.plugins.camerax.AspectRatioStrategyFallbackRuleDio.flutter.plugins.camerax.AspectRatioStrategyFallbackRule.Companion/<EMAIL><io.flutter.plugins.camerax.PigeonApiVideoRecordEventFinalize1io.flutter.plugins.camerax.PigeonApiMeteringPoint;io.flutter.plugins.camerax.PigeonApiMeteringPoint.Companion,io.flutter.plugins.camerax.PigeonApiObserver6io.flutter.plugins.camerax.PigeonApiObserver.Companion.io.flutter.plugins.camerax.PigeonApiCameraInfo8io.flutter.plugins.camerax.PigeonApiCameraInfo.Companion2io.flutter.plugins.camerax.PigeonApiCameraSelector<io.flutter.plugins.camerax.PigeonApiCameraSelector.Companion9io.flutter.plugins.camerax.PigeonApiProcessCameraProviderCio.flutter.plugins.camerax.PigeonApiProcessCameraProvider.Companion+io.flutter.plugins.camerax.PigeonApiUseCase*io.flutter.plugins.camerax.PigeonApiCamera4io.flutter.plugins.camerax.PigeonApiCamera.Companion9io.flutter.plugins.camerax.PigeonApiSystemServicesManagerCio.flutter.plugins.camerax.PigeonApiSystemServicesManager.Companion:io.flutter.plugins.camerax.PigeonApiCameraPermissionsError<io.flutter.plugins.camerax.PigeonApiDeviceOrientationManagerFio.flutter.plugins.camerax.PigeonApiDeviceOrientationManager.Companion+io.flutter.plugins.camerax.PigeonApiPreview5io.flutter.plugins.camerax.PigeonApiPreview.Companion0io.flutter.plugins.camerax.PigeonApiVideoCapture:io.flutter.plugins.camerax.PigeonApiVideoCapture.Companion/io.flutter.plugins.camerax.PigeonApiVideoOutput,io.flutter.plugins.camerax.PigeonApiRecorder6io.flutter.plugins.camerax.PigeonApiRecorder.Companion<io.flutter.plugins.camerax.PigeonApiVideoRecordEventListenerFio.flutter.plugins.camerax.PigeonApiVideoRecordEventListener.Companion4io.flutter.plugins.camerax.PigeonApiPendingRecording>io.flutter.plugins.camerax.PigeonApiPendingRecording.Companion-io.flutter.plugins.camerax.PigeonApiRecording7io.flutter.plugins.camerax.PigeonApiRecording.Companion0io.flutter.plugins.camerax.PigeonApiImageCapture:<EMAIL>@io.flutter.plugins.camerax.PigeonApiResolutionSelector.Companion7io.flutter.plugins.camerax.PigeonApiAspectRatioStrategyAio.flutter.plugins.camerax.PigeonApiAspectRatioStrategy.Companion/io.flutter.plugins.camerax.PigeonApiCameraState1io.flutter.plugins.camerax.PigeonApiExposureState-io.flutter.plugins.camerax.PigeonApiZoomState1io.flutter.plugins.camerax.PigeonApiImageAnalysis;io.flutter.plugins.camerax.PigeonApiImageAnalysis.Companion,io.flutter.plugins.camerax.PigeonApiAnalyzer6io.flutter.plugins.camerax.PigeonApiAnalyzer.Companion9io.flutter.plugins.camerax.PigeonApiCameraStateStateError,io.flutter.plugins.camerax.PigeonApiLiveData6io.flutter.plugins.camerax.PigeonApiLiveData.Companion.io.flutter.plugins.camerax.PigeonApiImageProxy8io.flutter.plugins.camerax.PigeonApiImageProxy.Companion.io.flutter.plugins.camerax.PigeonApiPlaneProxy3io.flutter.plugins.camerax.PigeonApiQualitySelector=io.flutter.plugins.camerax.PigeonApiQualitySelector.Companion4io.flutter.plugins.camerax.PigeonApiFallbackStrategy>io.flutter.plugins.camerax.PigeonApiFallbackStrategy.Companion1io.flutter.plugins.camerax.PigeonApiCameraControl;io.flutter.plugins.camerax.PigeonApiCameraControl.Companion>io.flutter.plugins.camerax.PigeonApiFocusMeteringActionBuilderHio.flutter.plugins.camerax.PigeonApiFocusMeteringActionBuilder.Companion7io.flutter.plugins.camerax.PigeonApiFocusMeteringAction7io.flutter.plugins.camerax.PigeonApiFocusMeteringResult2io.flutter.plugins.camerax.PigeonApiCaptureRequest<io.flutter.plugins.camerax.PigeonApiCaptureRequest.Companion5io.flutter.plugins.camerax.PigeonApiCaptureRequestKey9io.flutter.plugins.camerax.PigeonApiCaptureRequestOptionsCio.flutter.plugins.camerax.PigeonApiCaptureRequestOptions.Companion8io.flutter.plugins.camerax.PigeonApiCamera2CameraControlBio.flutter.plugins.camerax.PigeonApiCamera2CameraControl.Companion4io.flutter.plugins.camerax.PigeonApiResolutionFilter>io.flutter.plugins.camerax.PigeonApiResolutionFilter.Companion<io.flutter.plugins.camerax.PigeonApiCameraCharacteristicsKey9io.flutter.plugins.camerax.PigeonApiCameraCharacteristicsCio.flutter.plugins.camerax.PigeonApiCameraCharacteristics.Companion5io.flutter.plugins.camerax.PigeonApiCamera2CameraInfo?io.flutter.plugins.camerax.PigeonApiCamera2CameraInfo.Companion8io.flutter.plugins.camerax.PigeonApiMeteringPointFactoryBio.flutter.plugins.camerax.PigeonApiMeteringPointFactory.CompanionGio.flutter.plugins.camerax.PigeonApiDisplayOrientedMeteringPointFactoryQio.flutter.plugins.camerax.PigeonApiDisplayOrientedMeteringPointFactory.Companion'io.flutter.plugins.camerax.ResultCompat1io.flutter.plugins.camerax.ResultCompat.Companion                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                