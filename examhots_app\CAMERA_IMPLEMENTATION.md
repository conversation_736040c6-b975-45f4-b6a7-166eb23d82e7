# Implementasi Kamera untuk Soal Esai

## Deskripsi
Fitur ini memungkinkan siswa untuk mengambil foto langsung dari kamera saat mengerjakan soal esai pada aplikasi Android.

## Perubahan yang Dilakukan

### 1. <PERSON><PERSON><PERSON><PERSON> Dependencies
- Menambahkan plugin `camera: ^0.11.2` ke `pubspec.yaml`
- Plugin `image_picker: ^1.1.2` sudah ada sebelumnya dan digunakan untuk mengakses kamera

### 2. Modifikasi File `lib/pages/student/exam_page.dart`

#### Import yang Ditambahkan:
```dart
import 'package:image_picker/image_picker.dart';
```

#### Fungsi Baru yang Di<PERSON>bahkan:
```dart
Future<void> _takePhotoFromCamera(int questionId, List<File> currentImages) async
```

Fungsi ini:
- Menggunakan `ImagePicker` untuk mengakses kamera
- Mengatur kualitas gambar (80%) dan resolusi maksimal (1920x1080)
- Menambahkan foto yang diambil ke daftar gambar jawaban esai
- Menampilkan notifikasi sukses/error menggunakan SnackBar
- Menyimpan jawaban secara otomatis setelah foto diambil

#### Modifikasi Tombol "Ambil Foto":
Tombol yang sebelumnya hanya menampilkan pesan "Akan segera hadir!" sekarang berfungsi untuk:
- Membuka kamera device
- Mengambil foto
- Menyimpan foto ke jawaban esai

### 3. Fitur yang Tersedia

#### Untuk Siswa:
1. **Mengambil Foto**: Klik tombol "Ambil Foto" untuk membuka kamera
2. **Preview Foto**: Foto yang diambil langsung ditampilkan di bawah area jawaban
3. **Hapus Foto**: Dapat menghapus foto yang tidak diinginkan
4. **Multiple Photos**: Dapat mengambil beberapa foto untuk satu soal esai

#### Kualitas dan Pengaturan:
- **Kualitas**: 80% (balance antara kualitas dan ukuran file)
- **Resolusi Maksimal**: 1920x1080 pixels
- **Format**: JPEG (default dari ImagePicker)

### 4. Permissions yang Diperlukan

File `android/app/src/main/AndroidManifest.xml` sudah memiliki permission yang diperlukan:
```xml
<uses-permission android:name="android.permission.CAMERA" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### 5. Error Handling

Implementasi mencakup penanganan error untuk:
- Kamera tidak tersedia
- Permission ditolak
- Gagal menyimpan file
- Error sistem lainnya

Semua error ditampilkan kepada user melalui SnackBar dengan pesan yang informatif.

### 6. User Experience

#### Flow Penggunaan:
1. Siswa membuka soal esai
2. Mengetik jawaban di text field (opsional)
3. Klik tombol "Ambil Foto"
4. Kamera terbuka otomatis
5. Ambil foto dan konfirmasi
6. Foto ditampilkan di bawah area jawaban
7. Dapat mengambil foto tambahan atau menghapus foto yang tidak diinginkan
8. Jawaban (text + foto) tersimpan otomatis

#### Feedback ke User:
- ✅ "Foto berhasil diambil" - ketika foto berhasil diambil
- ❌ "Error mengambil foto: [detail error]" - ketika terjadi error
- ✅ "Gambar berhasil dihapus" - ketika foto berhasil dihapus

## Troubleshooting

### Error: Android SDK Version Issues
Jika muncul error seperti:
```
The plugin camera_android_camerax requires Android SDK version 35 or higher
```

**Solusi:**
Update `android/app/build.gradle.kts`:
```kotlin
android {
    compileSdk = 35
    defaultConfig {
        targetSdk = 35
        // ...
    }
}
```

### Error: Developer Mode Required
Jika muncul error:
```
Building with plugins requires symlink support.
Please enable Developer Mode in your system settings.
```

**Solusi:**
1. Buka Windows Settings
2. Pilih Update & Security → For developers
3. Aktifkan Developer mode
4. Restart command prompt/IDE

### Error: NullPointerException callGcSupression
**Solusi:**
1. Test di device fisik, bukan emulator
2. Pastikan permissions kamera sudah diberikan
3. Clean dan rebuild project

## Testing

Untuk menguji fitur ini:
1. **Aktifkan Developer Mode** di Windows
2. **Jalankan aplikasi di device Android fisik** (kamera tidak tersedia di emulator)
3. Login sebagai siswa
4. Mulai ujian yang memiliki soal esai
5. Klik tombol "Ambil Foto"
6. Verifikasi kamera terbuka dan foto dapat diambil
7. Verifikasi foto ditampilkan di interface
8. Test hapus foto
9. Test submit jawaban dengan foto

### Build Commands
```bash
# Clean project
flutter clean

# Get dependencies
flutter pub get

# Build debug APK
flutter build apk --debug

# Install to device
flutter install
```

## Catatan Teknis

- Menggunakan `ImagePicker.pickImage()` dengan `source: ImageSource.camera`
- File foto disimpan sementara di device storage
- Foto akan diupload ke server saat submit jawaban
- Implementasi mengikuti pattern yang sudah ada di aplikasi
- **Requires Android SDK 35+** untuk compatibility dengan camera plugins
- Compatible dengan Android API level 21+ (minSdk)
- Optimized untuk performance dengan reduced image quality dan resolution
